from typing import Any, Dict, Optional, Union, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime

from app.models.market import Market, MarketParticipant, Bidding, StorageBidding
from app.schemas.market import MarketCreate, MarketUpdate, MarketParticipantCreate, BiddingCreate, StorageBiddingCreate

def get_market(db: Session, market_id: int) -> Optional[Market]:
    return db.query(Market).filter(Market.id == market_id).first()

def get_markets(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    is_published: Optional[bool] = None,
    status: Optional[str] = None,
    type: Optional[str] = None,
    time_scale: Optional[str] = None,
    user_type: Optional[str] = None,
    search: Optional[str] = None
) -> List[Market]:
    query = db.query(Market)

    # 应用过滤条件
    if is_published is not None:
        query = query.filter(Market.is_published == is_published)

    if status:
        query = query.filter(Market.status == status)

    if type:
        query = query.filter(Market.type == type)

    if time_scale:
        query = query.filter(Market.time_scale == time_scale)

    if user_type:
        # 过滤允许特定用户类型参与的市场
        query = query.filter(Market.allowed_user_types.contains(user_type))

    if search:
        # 搜索市场名称或描述
        query = query.filter(
            or_(
                Market.name.ilike(f"%{search}%"),
                Market.description.ilike(f"%{search}%")
            )
        )

    return query.order_by(Market.created_at.desc()).offset(skip).limit(limit).all()

def create_market(db: Session, market_in: MarketCreate, creator_id: Optional[int] = None) -> Market:
    market_data = market_in.dict()
    db_market = Market(**market_data, creator_id=creator_id)
    db.add(db_market)
    db.commit()
    db.refresh(db_market)
    return db_market

def update_market(
    db: Session,
    db_market: Market,
    market_in: Union[MarketUpdate, Dict[str, Any]]
) -> Market:
    if isinstance(market_in, dict):
        update_data = market_in
    else:
        update_data = market_in.dict(exclude_unset=True)

    for field in update_data:
        if field in update_data:
            setattr(db_market, field, update_data[field])

    db.add(db_market)
    db.commit()
    db.refresh(db_market)
    return db_market

def delete_market(db: Session, market_id: int) -> bool:
    market = db.query(Market).filter(Market.id == market_id).first()
    if not market:
        return False
    db.delete(market)
    db.commit()
    return True

def publish_market(db: Session, db_market: Market) -> Market:
    db_market.publish()
    db.add(db_market)
    db.commit()
    db.refresh(db_market)
    return db_market

def end_market(db: Session, db_market: Market) -> Market:
    db_market.end()
    db.add(db_market)
    db.commit()
    db.refresh(db_market)
    return db_market

def update_market_status(db: Session, db_market: Market) -> Market:
    db_market.update_status()
    db.add(db_market)
    db.commit()
    db.refresh(db_market)
    return db_market

def get_market_participant(db: Session, market_id: int, user_id: int) -> Optional[MarketParticipant]:
    return db.query(MarketParticipant).filter(
        and_(
            MarketParticipant.market_id == market_id,
            MarketParticipant.user_id == user_id
        )
    ).first()

def get_market_participants(db: Session, market_id: int) -> List[MarketParticipant]:
    return db.query(MarketParticipant).filter(MarketParticipant.market_id == market_id).all()

def create_market_participant(db: Session, participant_in: MarketParticipantCreate) -> MarketParticipant:
    db_participant = MarketParticipant(
        market_id=participant_in.market_id,
        user_id=participant_in.user_id
    )
    db.add(db_participant)
    db.commit()
    db.refresh(db_participant)
    return db_participant

def delete_market_participant(db: Session, market_id: int, user_id: int) -> bool:
    participant = db.query(MarketParticipant).filter(
        and_(
            MarketParticipant.market_id == market_id,
            MarketParticipant.user_id == user_id
        )
    ).first()
    if not participant:
        return False
    db.delete(participant)
    db.commit()
    return True


# 报价相关的CRUD操作
def get_bidding(db: Session, bidding_id: int) -> Optional[Bidding]:
    """获取单个报价"""
    return db.query(Bidding).filter(Bidding.id == bidding_id).first()


def get_biddings(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    participant_id: Optional[int] = None,
    user_id: Optional[int] = None,
    status: Optional[str] = None
) -> List[Bidding]:
    """获取报价列表"""
    query = db.query(Bidding)

    # 应用过滤条件
    if market_id:
        query = query.filter(Bidding.market_id == market_id)

    if participant_id:
        query = query.filter(Bidding.participant_id == participant_id)

    if user_id:
        # 通过用户ID查询参与者，然后查询报价
        participant_ids = db.query(MarketParticipant.id).filter(
            MarketParticipant.user_id == user_id
        ).all()
        participant_ids = [p[0] for p in participant_ids]
        if participant_ids:
            query = query.filter(Bidding.participant_id.in_(participant_ids))
        else:
            # 如果用户没有参与任何市场，返回空列表
            return []

    if status:
        query = query.filter(Bidding.status == status)

    return query.order_by(Bidding.created_at.desc()).offset(skip).limit(limit).all()


def get_biddings_with_details(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    user_id: Optional[int] = None,
    status: Optional[str] = None
) -> List[Dict[str, Any]]:
    """获取带有详细信息的报价列表"""
    # 构建基本查询
    query = db.query(
        Bidding,
        Market.name.label("market_name"),
        MarketParticipant.user_id,
    ).join(
        Market, Bidding.market_id == Market.id
    ).join(
        MarketParticipant, Bidding.participant_id == MarketParticipant.id
    )

    # 应用过滤条件
    if market_id:
        query = query.filter(Bidding.market_id == market_id)

    if user_id:
        query = query.filter(MarketParticipant.user_id == user_id)

    if status:
        query = query.filter(Bidding.status == status)

    # 执行查询
    results = query.order_by(Bidding.created_at.desc()).offset(skip).limit(limit).all()

    # 构建结果
    biddings_with_details = []
    for bidding, market_name, user_id in results:
        # 获取用户信息
        participant = db.query(MarketParticipant).filter(MarketParticipant.id == bidding.participant_id).first()
        user = participant.user if participant else None

        if user:
            bidding_dict = {
                "id": bidding.id,
                "market_id": bidding.market_id,
                "market_name": market_name,
                "participant_id": bidding.participant_id,
                "user_id": user_id,
                "username": user.username,
                "user_type": user.user_type,
                "company_name": user.company_name,
                "period": bidding.period,
                "quantity": bidding.quantity,
                "price": bidding.price,
                "status": bidding.status,
                "remarks": bidding.remarks,
                "created_at": bidding.created_at,
                "updated_at": bidding.updated_at
            }
            biddings_with_details.append(bidding_dict)

    return biddings_with_details


def create_bidding(db: Session, bidding_in: BiddingCreate) -> Bidding:
    """创建报价"""
    db_bidding = Bidding(
        market_id=bidding_in.market_id,
        participant_id=bidding_in.participant_id,
        period=bidding_in.period,
        quantity=bidding_in.quantity,
        price=bidding_in.price,
        remarks=bidding_in.remarks
    )
    db.add(db_bidding)
    db.commit()
    db.refresh(db_bidding)
    return db_bidding


def update_bidding_status(
    db: Session,
    db_bidding: Bidding,
    status: str,
    remarks: Optional[str] = None
) -> Bidding:
    """更新报价状态"""
    db_bidding.status = status
    if remarks:
        db_bidding.remarks = remarks
    db.add(db_bidding)
    db.commit()
    db.refresh(db_bidding)
    return db_bidding


def delete_bidding(db: Session, bidding_id: int) -> bool:
    """删除报价"""
    bidding = db.query(Bidding).filter(Bidding.id == bidding_id).first()
    if not bidding:
        return False
    db.delete(bidding)
    db.commit()
    return True


# 储能报价相关的CRUD操作
def get_storage_bidding(db: Session, bidding_id: int) -> Optional[StorageBidding]:
    """获取单个储能报价"""
    return db.query(StorageBidding).filter(StorageBidding.id == bidding_id).first()


def get_storage_bidding_by_participant_and_period(
    db: Session,
    participant_id: int,
    market_id: int,
    period: int
) -> Optional[StorageBidding]:
    """根据参与者ID、市场ID和时段获取储能报价"""
    return db.query(StorageBidding).filter(
        and_(
            StorageBidding.participant_id == participant_id,
            StorageBidding.market_id == market_id,
            StorageBidding.period == period
        )
    ).first()


def get_storage_biddings(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    participant_id: Optional[int] = None,
    user_id: Optional[int] = None,
    status: Optional[str] = None
) -> List[StorageBidding]:
    """获取储能报价列表"""
    query = db.query(StorageBidding)

    # 应用过滤条件
    if market_id:
        query = query.filter(StorageBidding.market_id == market_id)

    if participant_id:
        query = query.filter(StorageBidding.participant_id == participant_id)

    if user_id:
        # 通过用户ID查询参与者，然后查询报价
        participant_ids = db.query(MarketParticipant.id).filter(
            MarketParticipant.user_id == user_id
        ).all()
        participant_ids = [p[0] for p in participant_ids]
        if participant_ids:
            query = query.filter(StorageBidding.participant_id.in_(participant_ids))
        else:
            # 如果用户没有参与任何市场，返回空列表
            return []

    if status:
        query = query.filter(StorageBidding.status == status)

    return query.order_by(StorageBidding.created_at.desc()).offset(skip).limit(limit).all()


def get_storage_biddings_with_details(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    user_id: Optional[int] = None,
    status: Optional[str] = None
) -> List[Dict[str, Any]]:
    """获取带有详细信息的储能报价列表"""
    # 构建基本查询
    query = db.query(
        StorageBidding,
        Market.name.label("market_name"),
        MarketParticipant.user_id,
    ).join(
        Market, StorageBidding.market_id == Market.id
    ).join(
        MarketParticipant, StorageBidding.participant_id == MarketParticipant.id
    )

    # 应用过滤条件
    if market_id:
        query = query.filter(StorageBidding.market_id == market_id)

    if user_id:
        query = query.filter(MarketParticipant.user_id == user_id)

    if status:
        query = query.filter(StorageBidding.status == status)

    # 执行查询
    results = query.order_by(StorageBidding.created_at.desc()).offset(skip).limit(limit).all()

    # 转换为字典格式
    biddings_with_details = []
    for bidding, market_name, user_id in results:
        # 获取用户信息
        from app.models.user import User
        user = db.query(User).filter(User.id == user_id).first()

        bidding_dict = {
            "id": bidding.id,
            "market_id": bidding.market_id,
            "market_name": market_name,
            "participant_id": bidding.participant_id,
            "period": bidding.period,
            "charge_price": bidding.charge_price,
            "discharge_price": bidding.discharge_price,
            "status": bidding.status,
            "remarks": bidding.remarks,
            "created_at": bidding.created_at,
            "updated_at": bidding.updated_at,
            "user_id": user_id,
            "username": user.username if user else "",
            "user_type": user.user_type if user else "",
            "company_name": user.company_name if user else ""
        }
        biddings_with_details.append(bidding_dict)

    return biddings_with_details


def create_storage_bidding(db: Session, bidding_in: StorageBiddingCreate) -> StorageBidding:
    """创建储能报价"""
    db_bidding = StorageBidding(
        market_id=bidding_in.market_id,
        participant_id=bidding_in.participant_id,
        period=bidding_in.period,
        charge_price=bidding_in.charge_price,
        discharge_price=bidding_in.discharge_price,
        remarks=bidding_in.remarks
    )
    db.add(db_bidding)
    db.commit()
    db.refresh(db_bidding)
    return db_bidding


def update_storage_bidding_status(
    db: Session,
    db_bidding: StorageBidding,
    status: str,
    remarks: Optional[str] = None
) -> StorageBidding:
    """更新储能报价状态"""
    db_bidding.status = status
    if remarks:
        db_bidding.remarks = remarks
    db.add(db_bidding)
    db.commit()
    db.refresh(db_bidding)
    return db_bidding


def get_storage_biddings_for_export(db: Session, market_id: int) -> List[Dict[str, Any]]:
    """获取指定市场的储能用户报价数据用于导出"""
    from sqlalchemy import and_
    from ..models.user import User

    # 查询储能用户的报价数据
    query = db.query(
        StorageBidding.charge_price,
        StorageBidding.discharge_price,
        MarketParticipant.user_id,
        User.username
    ).join(
        MarketParticipant, StorageBidding.participant_id == MarketParticipant.id
    ).join(
        User, MarketParticipant.user_id == User.id
    ).filter(
        and_(
            StorageBidding.market_id == market_id,
            User.user_type == 'storage'
        )
    )

    results = query.all()

    # 转换为字典格式
    biddings_data = []
    for result in results:
        biddings_data.append({
            'user_id': result.user_id,
            'username': result.username,
            'charge_price': float(result.charge_price) if result.charge_price else 0.0,
            'discharge_price': float(result.discharge_price) if result.discharge_price else 0.0
        })

    return biddings_data


def export_storage_biddings_to_excel(biddings_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """将储能报价数据导出到Excel文件"""
    import os
    import openpyxl
    import time
    from pathlib import Path

    try:
        # Excel文件路径
        excel_path = Path("marketkernelcode/输入数据.xlsm")

        # 检查文件是否存在
        if not excel_path.exists():
            return {
                "success": False,
                "message": f"Excel文件不存在: {excel_path}",
                "exported_count": 0
            }

        # 尝试多次打开文件，处理文件被占用的情况
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 打开Excel文件
                workbook = openpyxl.load_workbook(excel_path, keep_vba=True)
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试
                    continue
                else:
                    return {
                        "success": False,
                        "message": f"Excel文件被其他程序占用，无法写入: {excel_path}",
                        "exported_count": 0
                    }

        # 检查工作表是否存在
        if 'DayAheadQuote' not in workbook.sheetnames:
            workbook.close()
            return {
                "success": False,
                "message": "工作表 'DayAheadQuote' 不存在",
                "exported_count": 0
            }

        worksheet = workbook['DayAheadQuote']

        # 导出数据计数
        exported_count = 0

        # 根据用户ID映射到Excel单元格
        # 按用户ID排序，确保按顺序写入
        sorted_biddings = sorted(biddings_data, key=lambda x: x['user_id'])

        for i, bidding in enumerate(sorted_biddings):
            user_id = bidding['user_id']
            charge_price = bidding['charge_price']
            discharge_price = bidding['discharge_price']

            # 前15个储能用户对应 C100-C114 和 D100-D114
            if i < 15:  # 只处理前15个用户
                row_index = 100 + i  # 第1个用户 -> 行100, 第2个用户 -> 行101, ...

                # 写入充电价格到C列
                worksheet[f'C{row_index}'] = charge_price
                # 写入放电价格到D列
                worksheet[f'D{row_index}'] = discharge_price

                exported_count += 1

        # 保存文件
        workbook.save(excel_path)
        workbook.close()

        return {
            "success": True,
            "message": f"成功导出 {exported_count} 条储能报价数据到 {excel_path}",
            "exported_count": exported_count,
            "file_path": str(excel_path)
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"导出Excel文件失败: {str(e)}",
            "exported_count": 0
        }


def delete_storage_bidding(db: Session, bidding_id: int) -> bool:
    """删除储能报价"""
    db_bidding = db.query(StorageBidding).filter(StorageBidding.id == bidding_id).first()
    if db_bidding:
        db.delete(db_bidding)
        db.commit()
        return True
    return False

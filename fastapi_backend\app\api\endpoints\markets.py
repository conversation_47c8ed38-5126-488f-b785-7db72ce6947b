from typing import Any, List, Optional, Dict
from datetime import datetime
import os
import shutil
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import or_

from app import crud, models, schemas
from app.api import deps

router = APIRouter()

@router.get("/", response_model=List[schemas.Market])
def read_markets(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    is_published: Optional[bool] = None,
    status: Optional[str] = None,
    type: Optional[str] = None,
    time_scale: Optional[str] = None,
    user_type: Optional[str] = None,
    search: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取市场列表。
    """
    markets = crud.market.get_markets(
        db,
        skip=skip,
        limit=limit,
        is_published=is_published,
        status=status,
        type=type,
        time_scale=time_scale,
        user_type=user_type,
        search=search
    )

    # 更新所有市场的状态
    for market in markets:
        market.update_status()

    # 重新查询以获取更新后的状态
    markets = crud.market.get_markets(
        db,
        skip=skip,
        limit=limit,
        is_published=is_published,
        status=status,
        type=type,
        time_scale=time_scale,
        user_type=user_type,
        search=search
    )

    return markets

@router.post("/", response_model=schemas.Market)
def create_market(
    *,
    db: Session = Depends(deps.get_db),
    market_in: schemas.MarketCreate,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    创建新市场。
    """
    market = crud.market.create_market(db, market_in=market_in, creator_id=current_user.id)
    return market

@router.get("/unpublished")
def read_unpublished_markets(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    page: int = None,
    page_size: int = None,
    search: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    获取未发布市场列表。
    """
    # 处理前端分页参数
    if page is not None and page_size is not None:
        skip = (page - 1) * page_size
        limit = page_size

    # 获取总数
    total_query = db.query(models.Market).filter(models.Market.is_published == False)
    if search:
        total_query = total_query.filter(
            or_(
                models.Market.name.ilike(f"%{search}%"),
                models.Market.description.ilike(f"%{search}%")
            )
        )
    total = total_query.count()

    # 获取分页数据
    markets = crud.market.get_markets(
        db,
        skip=skip,
        limit=limit,
        is_published=False,
        search=search
    )

    # 返回分页格式
    return {
        "results": markets,
        "count": total
    }

@router.get("/published")
def read_published_markets(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    page: int = None,
    page_size: int = None,
    status: Optional[str] = None,
    type: Optional[str] = None,
    time_scale: Optional[str] = None,
    user_type: Optional[str] = None,
    search: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取已发布市场列表。
    """
    # 处理前端分页参数
    if page is not None and page_size is not None:
        skip = (page - 1) * page_size
        limit = page_size

    # 获取总数
    total_query = db.query(models.Market).filter(models.Market.is_published == True)
    if status:
        total_query = total_query.filter(models.Market.status == status)
    if type:
        total_query = total_query.filter(models.Market.type == type)
    if time_scale:
        total_query = total_query.filter(models.Market.time_scale == time_scale)
    if user_type:
        total_query = total_query.filter(models.Market.allowed_user_types.contains(user_type))
    if search:
        total_query = total_query.filter(
            or_(
                models.Market.name.ilike(f"%{search}%"),
                models.Market.description.ilike(f"%{search}%")
            )
        )
    total = total_query.count()

    # 获取分页数据
    markets = crud.market.get_markets(
        db,
        skip=skip,
        limit=limit,
        is_published=True,
        status=status,
        type=type,
        time_scale=time_scale,
        user_type=user_type,
        search=search
    )

    # 更新所有市场的状态
    for market in markets:
        market.update_status()
        db.add(market)
    db.commit()

    # 重新查询以获取更新后的状态
    markets = crud.market.get_markets(
        db,
        skip=skip,
        limit=limit,
        is_published=True,
        status=status,
        type=type,
        time_scale=time_scale,
        user_type=user_type,
        search=search
    )

    # 返回分页格式
    return {
        "results": markets,
        "count": total
    }



@router.put("/{market_id}", response_model=schemas.Market)
def update_market(
    *,
    db: Session = Depends(deps.get_db),
    market_id: int,
    market_in: schemas.MarketUpdate,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    更新市场。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )
    if market.is_published:
        raise HTTPException(
            status_code=400,
            detail="已发布的市场不能修改",
        )
    market = crud.market.update_market(db, db_market=market, market_in=market_in)
    return market

@router.delete("/{market_id}", response_model=dict)
def delete_market(
    *,
    db: Session = Depends(deps.get_db),
    market_id: int,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    删除市场。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )
    if market.is_published:
        raise HTTPException(
            status_code=400,
            detail="已发布的市场不能删除",
        )
    crud.market.delete_market(db, market_id=market_id)
    return {"detail": "市场删除成功"}

@router.post("/{market_id}/publish", response_model=schemas.Market)
def publish_market(
    *,
    db: Session = Depends(deps.get_db),
    market_id: int,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    发布市场。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )
    if market.is_published:
        raise HTTPException(
            status_code=400,
            detail="市场已经发布",
        )
    market = crud.market.publish_market(db, db_market=market)
    return market

@router.post("/{market_id}/terminate", response_model=Dict[str, Any])
def terminate_market(
    *,
    db: Session = Depends(deps.get_db),
    market_id: int,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    终止市场并导出储能用户报价数据到Excel文件。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )
    if market.status == 'ended':
        raise HTTPException(
            status_code=400,
            detail="市场已经结束",
        )

    try:
        # 获取储能用户报价数据
        storage_biddings = crud.market.get_storage_biddings_for_export(db, market_id=market_id)

        # 导出数据到Excel文件
        export_result = crud.market.export_storage_biddings_to_excel(storage_biddings)

        # 终止市场
        market = crud.market.end_market(db, db_market=market)

        return {
            "market": market,
            "export_result": export_result,
            "detail": "市场已终止，储能报价数据已导出"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"终止市场失败: {str(e)}",
        )

@router.post("/{market_id}/join", response_model=schemas.MarketParticipant)
def join_market(
    *,
    db: Session = Depends(deps.get_db),
    market_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    加入市场。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )
    if not market.is_published:
        raise HTTPException(
            status_code=400,
            detail="市场未发布",
        )
    if market.status == 'ended':
        raise HTTPException(
            status_code=400,
            detail="市场已结束",
        )

    # 检查用户类型是否允许参与
    allowed_types = market.allowed_user_types.split(',')
    if current_user.user_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail="您的用户类型不允许参与此市场",
        )

    # 检查是否已经参与
    participant = crud.market.get_market_participant(db, market_id=market_id, user_id=current_user.id)
    if participant:
        raise HTTPException(
            status_code=400,
            detail="您已经参与此市场",
        )

    # 检查参与人数是否已满
    participants = crud.market.get_market_participants(db, market_id=market_id)
    if len(participants) >= market.max_participants:
        raise HTTPException(
            status_code=400,
            detail="市场参与人数已满",
        )

    participant_in = schemas.MarketParticipantCreate(market_id=market_id, user_id=current_user.id)
    participant = crud.market.create_market_participant(db, participant_in=participant_in)
    return participant

@router.post("/{market_id}/leave", response_model=dict)
def leave_market(
    *,
    db: Session = Depends(deps.get_db),
    market_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    离开市场。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )

    # 检查是否已经参与
    participant = crud.market.get_market_participant(db, market_id=market_id, user_id=current_user.id)
    if not participant:
        raise HTTPException(
            status_code=400,
            detail="您未参与此市场",
        )

    crud.market.delete_market_participant(db, market_id=market_id, user_id=current_user.id)
    return {"detail": "已成功退出市场"}

@router.get("/{market_id}/participants", response_model=List[schemas.MarketParticipantWithUser])
def get_market_participants(
    market_id: int,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取市场参与者列表，包含用户详细信息。
    """
    # 检查市场是否存在
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )

    # 获取市场允许的用户类型
    allowed_user_types = []
    if market.allowed_user_types:
        allowed_user_types = market.allowed_user_types.split(",")

    # 如果没有指定允许的用户类型，则默认允许所有类型
    if not allowed_user_types:
        allowed_user_types = ["type1", "type2"]

    # 获取所有符合条件的用户
    users = db.query(models.User).filter(models.User.user_type.in_(allowed_user_types)).all()

    # 获取市场参与者
    participants = crud.market.get_market_participants(db, market_id=market_id)
    participant_user_ids = [p.user_id for p in participants]

    # 构建带有用户信息的列表
    result = []

    # 首先添加已经参与的用户
    for participant in participants:
        user = next((u for u in users if u.id == participant.user_id), None)
        if user:
            # 为不同用户类型分配不同的市场角色
            role = "generator"  # 默认为发电方
            if user.user_type == "type1":
                role = "generator"  # 类型1为发电方
            elif user.user_type == "type2":
                role = "consumer"  # 类型2为用电方

            # 根据用户ID生成一个伪随机的信用评级
            credit_ratings = ["AAA", "AA", "A", "BBB", "BB", "B"]
            credit_index = (user.id * 17) % len(credit_ratings)  # 使用质数17来增加随机性
            credit_rating = credit_ratings[credit_index]

            # 构建参与者信息
            participant_with_user = {
                "id": participant.id,
                "market_id": participant.market_id,
                "user_id": participant.user_id,
                "joined_at": participant.joined_at,
                "is_active": participant.is_active,
                "username": user.username,
                "email": user.email,
                "user_type": user.user_type,
                "phone_number": user.phone_number,
                "company_name": user.company_name,
                "credit_rating": credit_rating,
                "role": role,
                "is_participant": True
            }
            result.append(participant_with_user)

    # 然后添加符合条件但尚未参与的用户
    for user in users:
        if user.id not in participant_user_ids:
            # 为不同用户类型分配不同的市场角色
            role = "generator"  # 默认为发电方
            if user.user_type == "type1":
                role = "generator"  # 类型1为发电方
            elif user.user_type == "type2":
                role = "consumer"  # 类型2为用电方

            # 根据用户ID生成一个伪随机的信用评级
            credit_ratings = ["AAA", "AA", "A", "BBB", "BB", "B"]
            credit_index = (user.id * 17) % len(credit_ratings)  # 使用质数17来增加随机性
            credit_rating = credit_ratings[credit_index]

            # 构建用户信息
            user_info = {
                "id": None,  # 没有参与者ID
                "market_id": market_id,
                "user_id": user.id,
                "joined_at": None,  # 没有加入时间
                "is_active": False,  # 未激活
                "username": user.username,
                "email": user.email,
                "user_type": user.user_type,
                "phone_number": user.phone_number,
                "company_name": user.company_name,
                "credit_rating": credit_rating,
                "role": role,
                "is_participant": False
            }
            result.append(user_info)

    return result

@router.post("/{market_id}/upload-load-curve")
async def upload_load_curve(
    market_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    上传负荷曲线文件。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )

    # 创建媒体目录（如果不存在）
    media_dir = Path("media/load_curves")
    media_dir.mkdir(parents=True, exist_ok=True)

    # 生成文件名
    file_name = f"load_curve_{market_id}_{int(datetime.now().timestamp())}{Path(file.filename).suffix}"
    file_path = media_dir / file_name

    # 保存文件
    with file_path.open("wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    # 更新市场的负荷曲线文件路径
    file_url = f"/media/load_curves/{file_name}"
    market_update = {"load_curve_file": file_url}
    crud.market.update_market(db, db_market=market, market_in=market_update)

    return {"detail": "文件上传成功", "file_url": file_url}

@router.post("/biddings", response_model=Dict[str, Any])
def submit_bidding(
    bidding_data: schemas.BiddingBatch,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    提交报价。
    """
    market_id = bidding_data.market_id

    # 检查市场是否存在
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )

    # 检查市场状态
    if market.status == 'ended':
        raise HTTPException(
            status_code=400,
            detail="市场已结束，无法提交报价",
        )

    # 检查用户是否参与市场
    participant = crud.market.get_market_participant(db, market_id=market_id, user_id=current_user.id)
    if not participant:
        raise HTTPException(
            status_code=400,
            detail="您未参与此市场，无法提交报价",
        )

    # 保存报价
    saved_biddings = []
    for bidding_item in bidding_data.biddings:
        # 创建报价对象
        bidding_create = schemas.BiddingCreate(
            market_id=market_id,
            participant_id=participant.id,
            period=bidding_item.get("period"),
            quantity=bidding_item.get("quantity"),
            price=bidding_item.get("price"),
            remarks=bidding_item.get("remarks", "")
        )

        # 保存报价
        db_bidding = crud.market.create_bidding(db, bidding_create)
        saved_biddings.append({
            "id": db_bidding.id,
            "period": db_bidding.period,
            "quantity": db_bidding.quantity,
            "price": db_bidding.price,
            "status": db_bidding.status
        })

    return {"detail": "报价提交成功", "biddings": saved_biddings}


@router.get("/biddings", response_model=List[Dict[str, Any]])
def read_biddings(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    status: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户的报价列表。
    """
    # 获取当前用户的报价
    biddings = crud.market.get_biddings_with_details(
        db,
        skip=skip,
        limit=limit,
        market_id=market_id,
        user_id=current_user.id,
        status=status
    )

    return biddings


@router.get("/admin/biddings", response_model=List[Dict[str, Any]])
def read_all_biddings(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    user_id: Optional[int] = None,
    status: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    管理员获取所有报价列表。
    """
    # 获取所有报价
    biddings = crud.market.get_biddings_with_details(
        db,
        skip=skip,
        limit=limit,
        market_id=market_id,
        user_id=user_id,
        status=status
    )

    return biddings


@router.get("/biddings/{bidding_id}", response_model=Dict[str, Any])
def read_bidding(
    bidding_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取单个报价详情。
    """
    # 获取报价
    bidding = crud.market.get_bidding(db, bidding_id=bidding_id)
    if not bidding:
        raise HTTPException(
            status_code=404,
            detail="报价不存在",
        )

    # 检查权限
    participant = crud.market.get_market_participant(db, market_id=bidding.market_id, user_id=current_user.id)
    if not participant and not current_user.is_admin:
        raise HTTPException(
            status_code=403,
            detail="您没有权限查看此报价",
        )

    # 获取报价详情
    biddings_with_details = crud.market.get_biddings_with_details(
        db,
        market_id=bidding.market_id,
        skip=0,
        limit=1
    )

    if not biddings_with_details:
        raise HTTPException(
            status_code=404,
            detail="报价详情不存在",
        )

    return biddings_with_details[0]


@router.put("/admin/biddings/{bidding_id}/status", response_model=Dict[str, Any])
def update_bidding_status(
    bidding_id: int,
    status_data: Dict[str, str],
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    管理员更新报价状态。
    """
    # 获取报价
    bidding = crud.market.get_bidding(db, bidding_id=bidding_id)
    if not bidding:
        raise HTTPException(
            status_code=404,
            detail="报价不存在",
        )

    # 更新状态
    status = status_data.get("status")
    remarks = status_data.get("remarks")

    if not status:
        raise HTTPException(
            status_code=400,
            detail="状态不能为空",
        )

    # 检查状态是否有效
    valid_statuses = ["pending", "accepted", "rejected"]
    if status not in valid_statuses:
        raise HTTPException(
            status_code=400,
            detail=f"无效的状态，有效状态为: {', '.join(valid_statuses)}",
        )

    # 更新报价状态
    updated_bidding = crud.market.update_bidding_status(db, bidding, status, remarks)

    # 获取报价详情
    biddings_with_details = crud.market.get_biddings_with_details(
        db,
        market_id=bidding.market_id,
        skip=0,
        limit=1
    )

    if not biddings_with_details:
        raise HTTPException(
            status_code=404,
            detail="报价详情不存在",
        )

    return biddings_with_details[0]


@router.delete("/admin/biddings/{bidding_id}", response_model=Dict[str, Any])
def delete_bidding(
    bidding_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    管理员删除报价。
    """
    # 获取报价
    bidding = crud.market.get_bidding(db, bidding_id=bidding_id)
    if not bidding:
        raise HTTPException(
            status_code=404,
            detail="报价不存在",
        )

    # 删除报价
    result = crud.market.delete_bidding(db, bidding_id=bidding_id)
    if not result:
        raise HTTPException(
            status_code=400,
            detail="删除报价失败",
        )

    return {"detail": "报价已删除"}


# 储能报价相关API
@router.post("/storage/biddings", response_model=Dict[str, Any])
def submit_storage_bidding(
    bidding_data: schemas.StorageBiddingBatch,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    提交储能报价。
    """
    market_id = bidding_data.market_id

    # 检查市场是否存在
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )

    # 检查市场状态
    if market.status == 'ended':
        raise HTTPException(
            status_code=400,
            detail="市场已结束，无法提交报价",
        )

    # 检查用户是否为储能用户
    if current_user.user_type != 'storage':
        raise HTTPException(
            status_code=400,
            detail="只有储能用户可以提交储能报价",
        )

    # 检查用户是否参与市场
    participant = crud.market.get_market_participant(db, market_id=market_id, user_id=current_user.id)
    if not participant:
        raise HTTPException(
            status_code=400,
            detail="您未参与此市场，无法提交报价",
        )

    # 保存储能报价
    saved_biddings = []
    for bidding_item in bidding_data.biddings:
        period = bidding_item.get("period")

        # 检查是否已有该时段的报价
        existing_bidding = crud.market.get_storage_bidding_by_participant_and_period(
            db, participant_id=participant.id, market_id=market_id, period=period
        )

        if existing_bidding:
            # 更新现有报价
            existing_bidding.charge_price = bidding_item.get("charge_price")
            existing_bidding.discharge_price = bidding_item.get("discharge_price")
            existing_bidding.remarks = bidding_item.get("remarks", "")
            existing_bidding.updated_at = datetime.now()

            db.add(existing_bidding)
            db.commit()
            db.refresh(existing_bidding)

            db_bidding = existing_bidding
        else:
            # 创建新的储能报价对象
            bidding_create = schemas.StorageBiddingCreate(
                market_id=market_id,
                participant_id=participant.id,
                period=period,
                charge_price=bidding_item.get("charge_price"),
                discharge_price=bidding_item.get("discharge_price"),
                remarks=bidding_item.get("remarks", "")
            )

            # 保存报价
            db_bidding = crud.market.create_storage_bidding(db, bidding_create)

        saved_biddings.append({
            "id": db_bidding.id,
            "period": db_bidding.period,
            "charge_price": db_bidding.charge_price,
            "discharge_price": db_bidding.discharge_price,
            "status": db_bidding.status
        })

    return {"detail": "储能报价提交成功", "biddings": saved_biddings}


@router.get("/storage/biddings/{bidding_id}", response_model=Dict[str, Any])
def read_storage_bidding(
    bidding_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取单个储能报价详情。
    """
    # 获取储能报价
    bidding = crud.market.get_storage_bidding(db, bidding_id=bidding_id)
    if not bidding:
        raise HTTPException(
            status_code=404,
            detail="储能报价不存在",
        )

    # 检查权限 - 只有报价的用户或管理员可以查看
    participant = crud.market.get_market_participant(db, market_id=bidding.market_id, user_id=current_user.id)
    if not participant and not current_user.is_admin:
        raise HTTPException(
            status_code=403,
            detail="您没有权限查看此储能报价",
        )

    # 如果不是管理员，还需要检查是否是报价的参与者
    if not current_user.is_admin and participant.id != bidding.participant_id:
        raise HTTPException(
            status_code=403,
            detail="您没有权限查看此储能报价",
        )

    # 获取储能报价详情
    biddings_with_details = crud.market.get_storage_biddings_with_details(
        db,
        skip=0,
        limit=1,
        market_id=bidding.market_id,
        user_id=current_user.id
    )

    # 找到对应的报价
    target_bidding = None
    for b in biddings_with_details:
        if b.get('id') == bidding_id:
            target_bidding = b
            break

    if not target_bidding:
        raise HTTPException(
            status_code=404,
            detail="储能报价详情不存在",
        )

    return target_bidding


@router.get("/storage/biddings", response_model=List[Dict[str, Any]])
def read_storage_biddings(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    status: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户的储能报价列表。
    """
    # 获取当前用户的储能报价
    biddings = crud.market.get_storage_biddings_with_details(
        db,
        skip=skip,
        limit=limit,
        market_id=market_id,
        user_id=current_user.id,
        status=status
    )

    return biddings


@router.get("/admin/storage/biddings", response_model=List[Dict[str, Any]])
def read_all_storage_biddings(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    market_id: Optional[int] = None,
    user_id: Optional[int] = None,
    status: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    管理员获取所有储能报价列表。
    """
    # 获取所有储能报价
    biddings = crud.market.get_storage_biddings_with_details(
        db,
        skip=skip,
        limit=limit,
        market_id=market_id,
        user_id=user_id,
        status=status
    )

    return biddings


@router.get("/{market_id}", response_model=schemas.Market)
def read_market(
    market_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    通过ID获取市场。
    """
    market = crud.market.get_market(db, market_id=market_id)
    if not market:
        raise HTTPException(
            status_code=404,
            detail="市场不存在",
        )

    # 更新市场状态
    market.update_status()
    db.commit()

    return market
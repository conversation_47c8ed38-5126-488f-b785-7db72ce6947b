<template>
  <div class="published-markets">
    <h2>已发布市场</h2>

    <div class="table-actions">
      <el-input
        v-model="searchQuery"
        placeholder="搜索市场名称"
        style="width: 300px"
        clearable
        @clear="loadMarkets"
      >
        <template #prefix>
          <i class="el-icon-search"></i>
        </template>
      </el-input>

      <el-button type="primary" @click="loadMarkets">搜索</el-button>

      <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 150px" @change="loadMarkets">
        <el-option label="全部" value=""></el-option>
        <el-option label="进行中" value="active"></el-option>
        <el-option label="已结束" value="ended"></el-option>
        <el-option label="未开始" value="pending"></el-option>
      </el-select>
    </div>

    <el-table
      :data="markets"
      border
      style="width: 100%; min-width: 100%; table-layout: fixed;"
      v-loading="loading"
    >
      <el-table-column prop="id" label="ID" min-width="40"></el-table-column>
      <el-table-column prop="name" label="市场名称" min-width="120"></el-table-column>
      <el-table-column prop="time_scale" label="时间尺度" min-width="80">
        <template #default="scope">
          <el-tag type="info">{{ getTimeScaleLabel(scope.row.time_scale) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="product_type" label="交易品种" min-width="80">
        <template #default="scope">
          <el-tag type="success">{{ getProductTypeLabel(scope.row.product_type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="trade_type" label="交易类型" min-width="80">
        <template #default="scope">
          <el-tag type="warning">{{ getTradeTypeLabel(scope.row.trade_type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="bidding_method" label="报价方式" min-width="80">
        <template #default="scope">
          <el-tag type="primary">{{ getBiddingMethodLabel(scope.row.bidding_method) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlement_method" label="结算方式" min-width="120">
        <template #default="scope">
          <el-tag type="danger">{{ getSettlementMethodLabel(scope.row.settlement_method) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="start_date" label="开始日期" min-width="150">
        <template #default="scope">
          {{ formatDateTime(scope.row.start_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="end_date" label="结束日期" min-width="150">
        <template #default="scope">
          {{ formatDateTime(scope.row.end_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="70">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="published_at" label="发布时间" min-width="150">
        <template #default="scope">
          {{ formatDateTime(scope.row.published_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="120" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="viewMarket(scope.row)">查看</el-button>
          <el-button
            size="small"
            type="danger"
            @click="terminateMarket(scope.row)"
            :disabled="scope.row.status === 'ended'"
          >
            终止
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 表格数据
const markets = ref([])

// 分页相关
const total = ref(100)
const pageSize = ref(10)
const currentPage = ref(1)

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('')
const loading = ref(false)

// 获取时间尺度标签
const getTimeScaleLabel = (scale) => {
  const scaleMap = {
    'day_ahead': '日前'
  }
  return scaleMap[scale] || '未知'
}

// 获取交易品种标签
const getProductTypeLabel = (type) => {
  const typeMap = {
    'energy': '能量'
  }
  return typeMap[type] || '未知'
}

// 获取交易类型标签
const getTradeTypeLabel = (type) => {
  const typeMap = {
    'centralized_bidding': '集中竞价'
  }
  return typeMap[type] || '未知'
}

// 获取报价方式标签
const getBiddingMethodLabel = (method) => {
  const methodMap = {
    'one_side': '单边报价'
  }
  return methodMap[method] || '未知'
}

// 获取结算方式标签
const getSettlementMethodLabel = (method) => {
  const methodMap = {
    'nodal_marginal_price': '节点边际电价出清'
  }
  return methodMap[method] || '未知'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'active': '进行中',
    'ended': '已结束',
    'pending': '未开始'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    'active': 'success',
    'ended': 'info',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'

  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateTimeStr
  }
}

// 导入API服务
import { getPublishedMarkets, terminateMarket as terminateMarketApi } from '../../api/market'

// 加载市场数据
const loadMarkets = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 如果有搜索关键词，添加到参数中
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    // 如果有状态筛选，添加到参数中
    if (statusFilter.value) {
      params.status = statusFilter.value
    }

    // 调用API获取已发布市场列表
    const response = await getPublishedMarkets(params)

    console.log('已发布市场数据:', response)

    // 检查响应格式
    if (Array.isArray(response)) {
      // 后端直接返回数组
      markets.value = response
      total.value = response.length
    } else if (response.results) {
      // 后端返回分页对象
      markets.value = response.results
      total.value = response.count || 0
    } else {
      // 未知格式
      markets.value = []
      total.value = 0
      console.error('未知的响应格式:', response)
    }

    ElMessage.success('加载市场数据成功')
  } catch (error) {
    console.error('加载市场数据失败:', error)
    ElMessage.error('加载市场数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查看市场详情
const viewMarket = (market) => {
  // 将市场ID存储在本地存储中，以便详情页面可以获取
  localStorage.setItem('viewMarketId', market.id)
  router.push(`/market-results?id=${market.id}`)
}



// 终止市场
const terminateMarket = async (market) => {
  try {
    await ElMessageBox.confirm(
      `确定要终止市场 "${market.name}" 吗? 此操作将：\n1. 终止市场运行\n2. 获取储能用户报价数据\n3. 导出数据到Excel文件\n\n此操作不可逆!`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: false
      }
    )

    loading.value = true

    // 调用API终止市场
    const response = await terminateMarketApi(market.id)
    console.log('市场终止成功:', response)

    // 处理响应结果
    if (response.export_result) {
      if (response.export_result.success) {
        ElMessage.success({
          message: `市场已终止！${response.export_result.message}`,
          duration: 5000
        })
      } else {
        ElMessage.warning({
          message: `市场已终止，但数据导出失败：${response.export_result.message}`,
          duration: 5000
        })
      }
    } else {
      ElMessage.success('市场已终止')
    }

    // 延迟一下再刷新数据，确保后端数据已更新
    setTimeout(() => {
      loadMarkets()
    }, 500)
  } catch (error) {
    if (error.response) {
      ElMessage.error(`终止失败: ${error.response.data.detail || '未知错误'}`)
    } else if (error.message && error.message !== 'cancel') {
      ElMessage.error('终止失败，请重试')
    } else {
      // 用户取消操作
      console.log('取消终止')
    }
  } finally {
    loading.value = false
  }
}

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  loadMarkets()
}

// 当前页变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMarkets()
}

// 定时刷新间隔（毫秒）
const REFRESH_INTERVAL = 60000 // 1分钟

// 定时刷新定时器
let refreshTimer = null

// 组件挂载时加载数据并启动定时刷新
onMounted(() => {
  loadMarkets()

  // 启动定时刷新
  refreshTimer = setInterval(() => {
    loadMarkets()
  }, REFRESH_INTERVAL)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
.published-markets {
  padding: 20px 0;
  width: 100%;
  overflow-x: auto;
}

h2 {
  margin-bottom: 20px;
  font-weight: 500;
  color: #303133;
}

.table-actions {
  margin-bottom: 20px;
  display: flex;
  gap: 15px;
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  width: 100%;
}

:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}
</style>
